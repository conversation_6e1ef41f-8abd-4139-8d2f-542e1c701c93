[project]
name = "NexTranslation"
version = "0.3.0"
description = "An intelligent PDF translation tool that preserves layout"
authors = [
    {name = "NexTranslation Team", email = "<EMAIL>"}
]
readme = "README.md"
license = "GPL-3.0"
classifiers = [
    "Development Status :: 4 - Beta",
    "Programming Language :: Python :: 3.11",
    "Topic :: Text Processing :: Linguistic",
    "Topic :: Office/Business",
    "Topic :: Scientific/Engineering",
]
requires-python = ">=3.11,<3.13"
dependencies = [
    "pymupdf<1.25.3",
    "tqdm",
    "tenacity",
    "numpy",
    "gradio",
    "pdfminer.six>=20240706",
    "peewee>=3.17.8",
    "rich",
    "onnx",          
    "onnxruntime",  
    "requests",     
    "BabelDOC"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov",
    "pytest-mock",
    "pytest-asyncio",
    "black",
    "isort",
    "pyinstaller",
]
gui = [
    "pikepdf",
]

[project.urls]
"Homepage" = "https://github.com/Ao-chii/NexTranslation"
"Bug Tracker" = "https://github.com/Ao-chii/NexTranslation/issues"

[project.scripts]
nex-translate = "nex_translation.presentation.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/nex_translation"]